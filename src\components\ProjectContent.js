"use client";

import { useState } from 'react';

const ProjectContent = ({ project, projects, activeProjectIndex, opacity = 1, maskProgress = 0, blurAmount = 0 }) => {
  const [isHovered, setIsHovered] = useState(false);

  return (
    <div
      className="absolute inset-0 w-full h-full cursor-pointer"
      style={{
        opacity: opacity,
        // Mask reveals from right to left
        clipPath: `inset(0 0 0 ${(1 - maskProgress) * 100}%)`,
        // Blur effect during mask transition
        filter: blurAmount > 0 ? `blur(${blurAmount}px)` : 'none'
      }}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {/* Full background image with inner shadow on hover */}
      <div
        className="relative w-full h-full rounded-3xl overflow-hidden transition-all duration-300"
        style={{
          boxShadow: isHovered
            ? 'inset 0 0 60px rgba(0, 0, 0, 0.3)'
            : 'inset 0 0 0 rgba(0, 0, 0, 0)'
        }}
      >
        {project.image ? (
          <img
            src={project.image}
            alt={project.title}
            className="w-full h-full object-cover"
          />
        ) : (
          <div className="w-full h-full bg-primary/20 flex items-center justify-center">
            <span className="text-secondary text-lg font-medium">{project.title}</span>
          </div>
        )}
      </div>
    </div>
  );
};

export default ProjectContent;
