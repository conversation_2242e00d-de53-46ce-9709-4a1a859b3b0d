"use client";

import { useState } from 'react';

const ProjectContent = ({ project, projects, activeProjectIndex, opacity = 1, maskProgress = 0, blurAmount = 0 }) => {
  const [isHovered, setIsHovered] = useState(false);

  return (
    <div
      className="absolute inset-0 w-full h-full cursor-pointer"
      style={{
        opacity: opacity,
        // Mask reveals from right to left
        clipPath: `inset(0 0 0 ${(1 - maskProgress) * 100}%)`,
        // Blur effect during mask transition
        filter: blurAmount > 0 ? `blur(${blurAmount}px)` : 'none'
      }}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {/* Full background image */}
      <div className="relative w-full h-full rounded-3xl overflow-hidden">
        {project.image ? (
          <img
            src={project.image}
            alt={project.title}
            className="w-full h-full object-cover"
          />
        ) : (
          <div className="w-full h-full bg-primary/20 flex items-center justify-center">
            <span className="text-secondary text-lg font-medium">{project.title}</span>
          </div>
        )}

        {/* Inner shadow overlay on top of image */}
        <div
          className="absolute inset-0 rounded-3xl transition-all duration-300 pointer-events-none"
          style={{
            boxShadow: isHovered
              ? 'inset 0 0 60px rgba(0, 0, 0, 0.4)'
              : 'inset 0 0 0 rgba(0, 0, 0, 0)'
          }}
        />
      </div>
    </div>
  );
};

export default ProjectContent;
