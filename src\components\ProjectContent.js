"use client";

import { useState } from 'react';
import Button from './Button';

const ProjectContent = ({ project, projects, activeProjectIndex, opacity = 1, maskProgress = 0, blurAmount = 0 }) => {
  const [isHovered, setIsHovered] = useState(false);

  return (
    <div
      className="absolute inset-0 w-full h-full group cursor-pointer"
      style={{
        opacity: opacity,
        // Mask reveals from right to left
        clipPath: `inset(0 0 0 ${(1 - maskProgress) * 100}%)`,
        // Blur effect during mask transition
        filter: blurAmount > 0 ? `blur(${blurAmount}px)` : 'none'
      }}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {/* Full background image */}
      <div className="relative w-full h-full rounded-3xl overflow-hidden">
        {project.image ? (
          <img
            src={project.image}
            alt={project.title}
            className="w-full h-full object-cover"
          />
        ) : (
          <div className="w-full h-full bg-primary/20 flex items-center justify-center">
            <span className="text-secondary text-lg font-medium">{project.title}</span>
          </div>
        )}

        {/* Hover button in bottom right corner */}
        <div className="absolute bottom-4 right-4">
          <div className={`transition-all duration-300 ${
            isHovered ? 'opacity-100 scale-100' : 'opacity-0 scale-95'
          }`}>
            <Button variant="filled">
              View More
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProjectContent;
