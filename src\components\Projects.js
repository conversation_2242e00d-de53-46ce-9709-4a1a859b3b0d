"use client";

import { useRef, useState, useEffect } from 'react';
import ProjectCard from './ProjectCard';
import ProjectText from './ProjectText';
import SectionTitleAnimation from './SectionTitleAnimation';

const Projects = () => {
  const sectionRef = useRef(null);
  const [projectScrollProgress, setProjectScrollProgress] = useState(0);
  const [isProjectsComplete, setIsProjectsComplete] = useState(false);
  const [normalScrollProgress, setNormalScrollProgress] = useState(0);
  
  // Projects data array
  const projects = [
    {
      id: 1,
      title: "Theater Poster Collection",
      description: "Posters and social media visuals for shows like <PERSON>ul Lebedelor, Mamma Mia!, and The Nutcracker. Each design captures the unique vibe of the show, made for both print and digital use.",
      techStack: ['PS', 'AI', 'GPT'],
      image: "/Projects/Showcase/Poster_Mockup_3.png"
    },
    {
      id: 2,
      title: "E-Commerce Platform",
      description: "Full-stack e-commerce solution with user authentication, payment processing, and admin dashboard. Built for scalability and performance.",
      techStack: ['React', 'Node.js', 'MongoDB', 'Stripe']
    },
    {
      id: 3,
      title: "Task Management App",
      description: "Collaborative task management application with real-time updates, team collaboration features, and intuitive drag-and-drop interface.",
      techStack: ['Vue.js', 'Firebase', 'Vuetify', 'Socket.io']
    },
    {
      id: 4,
      title: "Weather Dashboard",
      description: "Interactive weather dashboard with location-based forecasts, historical data visualization, and customizable widgets for weather tracking.",
      techStack: ['React', 'D3.js', 'OpenWeather API', 'Chart.js']
    }
  ];
  
  useEffect(() => {
    const handleScroll = () => {
      if (!sectionRef.current) return;

      const projectsRect = sectionRef.current.getBoundingClientRect();
      const windowHeight = window.innerHeight;

      // Calculate how far we've scrolled into the Projects section
      const projectsTop = projectsRect.top;
      const projectsHeight = projectsRect.height;

      // Start project animations when Projects section reaches top of viewport
      const triggerOffset = windowHeight * 0.1;
      if (projectsTop <= triggerOffset) {
        // Calculate scroll progress through Projects section with three phases:
        // Phase 1: Project animations (0-75% of section height) - SLOW & SMOOTH
        // Phase 2: Settling period (75-80% of section height) - PAUSE
        // Phase 3: Normal scroll transition (80-100% = exactly 100vh) - NATURAL SPEED
        const scrolledIntoProjects = Math.abs(projectsTop - triggerOffset);

        const projectAnimationDistance = projectsHeight * 0.75; // 75% for smooth animations (300vh)
        const settlingDistance = projectsHeight * 0.05;         // 5% for settling (20vh)
        const normalScrollDistance = projectsHeight * 0.2;      // 20% for normal scroll (80vh)
        const totalAnimationDistance = projectAnimationDistance + settlingDistance;

        const rawScrollProgress = Math.min(1, scrolledIntoProjects / projectAnimationDistance);
        const settlingProgress = Math.min(1, Math.max(0, (scrolledIntoProjects - projectAnimationDistance) / settlingDistance));

        // Projects animation is complete after settling period finishes
        const projectsAnimationComplete = settlingProgress >= 1.0;

        if (!projectsAnimationComplete) {
          // Phase 1: Project animations (0% - 75% of section height)
          setIsProjectsComplete(false);
          setNormalScrollProgress(0);
          setProjectScrollProgress(rawScrollProgress);
        } else {
          // Phase 3: Projects complete (after settling), calculate normal scroll progress
          setIsProjectsComplete(true);
          setProjectScrollProgress(1); // Keep projects at final state

          // Calculate normal scroll progress for the remaining 20% of section height (80vh)
          const normalScrollStart = totalAnimationDistance; // Start after settling period
          const normalScrollAmount = Math.max(0, scrolledIntoProjects - normalScrollStart);
          const normalProgress = Math.min(1, normalScrollAmount / normalScrollDistance);

          setNormalScrollProgress(normalProgress);
        }
      } else {
        // Reset effects when Projects section hasn't reached the trigger point yet
        setProjectScrollProgress(0);
        setIsProjectsComplete(false);
        setNormalScrollProgress(0);
      }
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    // Check initial position
    handleScroll();

    return () => window.removeEventListener('scroll', handleScroll);
  }, []);
  
  // Animation variants moved to AnimatedFixedTitle component

  return (
    <>
      {/* Reusable Section Title Animation */}
      <SectionTitleAnimation
        title="Projects"
        currentSectionRef={sectionRef}
        previousSectionSelector='[data-section="services"]'
        zIndex="z-5"
        className="font-heading font-extrabold text-secondary text-4xl lg:text-6xl"
      />

      {/* Combined Projects Container - Single fixed div for both text and cards */}
      <div
        className="fixed inset-0 flex items-center justify-center z-10"
        style={{
          opacity: projectScrollProgress > 0 ? 1 : 0,
          transform: isProjectsComplete ? `translateY(${-normalScrollProgress * 100}vh)` : 'translateY(0)',
          pointerEvents: projectScrollProgress > 0.01 ? 'auto' : 'none'
        }}
      >
        {/* Main container - adjust w-9/10 to change overall width */}
        <div className="w-9/10 h-screen flex">
          {/* Project Text - Left Side (1/3 of container) */}
          <div className="w-1/3 h-full flex items-center pl-8 lg:pl-12 bg-primary">
            <div className="w-full">
              <ProjectText scrollProgress={projectScrollProgress} projects={projects} />
            </div>
          </div>

          {/* Project Card Area - Right Side (2/3 of container) */}
          <div className="w-2/3 h-full flex items-center pr-8 lg:pr-12 relative">
            <div className="w-full h-full bg-background">
              <ProjectCard scrollProgress={projectScrollProgress} projects={projects} />
            </div>
          </div>
        </div>
      </div>

      {/* Projects Section - provides scroll space */}
      <section
        ref={sectionRef}
        data-section="projects"
        className="bg-background py-20 min-h-[400vh]"
      >
        <div className="w-full mx-auto px-6">
          {/* Invisible spacer to provide scroll area */}
          <div className="h-[600vh]"></div>
        </div>
      </section>
    </>
  );
};

export default Projects;
